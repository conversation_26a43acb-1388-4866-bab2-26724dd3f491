import type { Settings } from '@/types';

export const APP_NAME = 'PestoApp';
export const SETTINGS_KEY = 'pesto-app-settings';
export const HISTORY_KEY = 'pesto-app-history';

export const DEFAULT_SETTINGS: Settings = {
  focusDuration: 25,
  shortBreakDuration: 5,
  longBreakDuration: 15,
  sessionsBeforeLongBreak: 4,
  clockStyle: 'digital',
  backgrounds: {
    focus: {
      url: 'https://images.unsplash.com/photo-1571790491176-918bb99ea860?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3OTE1NTh8MHwxfHNlYXJjaHwxN3x8Zm9jdXN8ZW58MHwwfHx8MTc1NTEzNTczNXww&ixlib=rb-4.1.0&q=80&w=1080',
      hint: 'desk study',
    },
    shortBreak: {
      url: 'https://images.unsplash.com/photo-1530224264768-7ff8c1789d79?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3OTE1NTh8MHwxfHNlYXJjaHw3fHxmb3Jlc3QlMjBwYXRofGVufDB8MHx8fDE3NTUxMzIwMzZ8MA&ixlib=rb-4.1.0&q=80&w=1080',
      hint: 'forest path',
    },
    longBreak: {
      url: 'https://images.unsplash.com/photo-1729479929945-476781fcb8a7?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3OTE1NTh8MHwxfHNlYXJjaHwyfHxiZWFjaCUyMHJlbGF4aW5nfGVufDB8MHx8fDE3NTUxMzI4MDJ8MA&ixlib=rb-4.1.0&q=80&w=1080',
      hint: 'beach relaxing',
    },
  },
  sounds: {
    focusEnd: '/sounds/bell-focus.mp3',
    shortBreakEnd: '/sounds/bell-short.mp3',
    longBreakEnd: '/sounds/bell-long.mp3',
  },
  notifications: {
    enabled: true,
  },
  theme: 'system',
  language: 'en',
  colorPalette: 'default',
};
