'use client';

import { createContext, useContext, useState, useEffect, type ReactNode, useCallback } from 'react';

type FullscreenProviderState = {
  isFullscreen: boolean;
  toggleFullscreen: () => void;
};

const FullscreenProviderContext = createContext<FullscreenProviderState | undefined>(undefined);

export function FullscreenProvider({ children }: { children: ReactNode }) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, []);
  
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const value = { isFullscreen, toggleFullscreen };

  return <FullscreenProviderContext.Provider value={value}>{children}</FullscreenProviderContext.Provider>;
}

export const useFullscreen = () => {
  const context = useContext(FullscreenProviderContext);
  if (context === undefined) {
    throw new Error('useFullscreen must be used within a FullscreenProvider');
  }
  return context;
};
