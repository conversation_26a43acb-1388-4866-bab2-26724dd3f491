'use client';

import { type ReactNode } from 'react';
import { ThemeProvider } from './theme-provider';
import { SettingsProvider } from './settings-provider';
import { TimerProvider } from './timer-provider';
import { LanguageProvider } from './language-provider';
import { FullscreenProvider } from './fullscreen-provider';

export function AppProviders({ children }: { children: ReactNode }) {
  return (
    <SettingsProvider>
      <ThemeProvider>
        <LanguageProvider>
          <FullscreenProvider>
              <TimerProvider>{children}</TimerProvider>
          </FullscreenProvider>
        </LanguageProvider>
      </ThemeProvider>
    </SettingsProvider>
  );
}
