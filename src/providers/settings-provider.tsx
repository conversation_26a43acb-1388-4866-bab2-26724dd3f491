'use client';

import { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import type { Settings } from '@/types';
import { DEFAULT_SETTINGS, SETTINGS_KEY } from '@/lib/constants';

type SettingsProviderState = {
  settings: Settings;
  setSettings: (settings: Partial<Settings>) => void;
  isLoaded: boolean;
};

const SettingsProviderContext = createContext<SettingsProviderState | undefined>(undefined);

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettingsState] = useState<Settings>(DEFAULT_SETTINGS);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    try {
      const storedSettings = localStorage.getItem(SETTINGS_KEY);
      if (storedSettings) {
        const parsed = JSON.parse(storedSettings);
        // Merge with defaults to prevent breakages on new settings
        setSettingsState(prev => ({ ...prev, ...parsed}));
      }
    } catch (error) {
      console.error("Failed to parse settings from localStorage", error);
    }
    setIsLoaded(true);
  }, []);

  const setSettings = (newSettings: Partial<Settings>) => {
    setSettingsState(prevSettings => {
      const updatedSettings = { ...prevSettings, ...newSettings };
      try {
        localStorage.setItem(SETTINGS_KEY, JSON.stringify(updatedSettings));
      } catch (error) {
        console.error("Failed to save settings to localStorage", error);
      }
      return updatedSettings;
    });
  };

  const value = { settings, setSettings, isLoaded };

  return <SettingsProviderContext.Provider value={value}>{children}</SettingsProviderContext.Provider>;
}

export const useSettings = () => {
  const context = useContext(SettingsProviderContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
