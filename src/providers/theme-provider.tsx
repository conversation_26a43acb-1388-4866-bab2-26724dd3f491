'use client';

import { createContext, useContext, useEffect, type ReactNode } from 'react';
import type { Theme, ColorPalette } from '@/types';
import { useSettings } from './settings-provider';

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  colorPalette: ColorPalette;
  setColorPalette: (palette: ColorPalette) => void;
};

const ThemeProviderContext = createContext<ThemeProviderState | undefined>(undefined);

export function ThemeProvider({ children }: { children: ReactNode }) {
  const { settings, setSettings, isLoaded } = useSettings();
  const { theme, colorPalette } = settings;

  const setTheme = (newTheme: Theme) => {
    setSettings({ theme: newTheme });
  };
  
  const setColorPalette = (newPalette: ColorPalette) => {
    setSettings({ colorPalette: newPalette });
  };
  
  useEffect(() => {
    if (!isLoaded) return;
    
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }
    
    // Handle color palettes
    root.classList.remove('theme-forest', 'theme-ocean', 'theme-sunset', 'theme-rose');
    if (colorPalette === 'forest') {
      root.classList.add('theme-forest');
    } else if (colorPalette === 'ocean') {
      root.classList.add('theme-ocean');
    } else if (colorPalette === 'sunset') {
        root.classList.add('theme-sunset');
    } else if (colorPalette === 'rose') {
        root.classList.add('theme-rose');
    }

  }, [theme, colorPalette, isLoaded]);

  const value = { theme, setTheme, colorPalette, setColorPalette };

  return <ThemeProviderContext.Provider value={value}>{children}</ThemeProviderContext.Provider>;
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
