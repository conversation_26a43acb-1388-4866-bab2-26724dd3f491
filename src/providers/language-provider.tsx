'use client';

import { createContext, useContext, useEffect, useState, type ReactNode, useCallback } from 'react';
import type { Language } from '@/types';
import { translations } from '@/lib/translations';

type LanguageProviderState = {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: keyof (typeof translations)['en']) => string;
};

const LanguageProviderContext = createContext<LanguageProviderState | undefined>(undefined);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en');

  useEffect(() => {
    const storedLang = localStorage.getItem('pesto-language') as Language | null;
    if (storedLang) {
      setLanguageState(storedLang);
    } else {
      const browserLang = navigator.language.split('-')[0];
      if (browserLang === 'es') {
        setLanguageState('es');
      }
    }
  }, []);

  const setLanguage = (newLanguage: Language) => {
    localStorage.setItem('pesto-language', newLanguage);
    setLanguageState(newLanguage);
  };
  
  const t = useCallback((key: keyof (typeof translations)['en']): string => {
    return translations[language][key] || translations['en'][key];
  }, [language]);


  const value = { language, setLanguage, t };

  return <LanguageProviderContext.Provider value={value}>{children}</LanguageProviderContext.Provider>;
}

export const useLanguage = () => {
  const context = useContext(LanguageProviderContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
