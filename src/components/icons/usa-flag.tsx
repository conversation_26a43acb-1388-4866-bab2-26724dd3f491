import { cn } from '@/lib/utils';
import type { SVGProps } from 'react';

export function UsaFlag(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 600" {...props}>
      <path fill="#fff" d="M0 0h900v600H0z"/>
      <path fill="#b22234" d="M0 0h900v323.077H0z" transform="scale(1 .15385)"/>
      <path fill="#b22234" d="M0 0h900v323.077H0z" transform="matrix(1 0 0 .15385 0 138.462)"/>
      <path fill="#b22234" d="M0 0h900v323.077H0z" transform="matrix(1 0 0 .15385 0 276.923)"/>
      <path fill="#b22234" d="M0 415.385h900v46.154H0z"/>
      <path fill="#b22234" d="M0 507.692h900v46.154H0z"/>
      <path fill="#3c3b6e" d="M0 0h400v323.077H0z"/>
    </svg>
  );
}
