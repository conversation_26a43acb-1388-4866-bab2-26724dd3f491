import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface FlipCardProps {
  value: string;
}

function FlipCard({ value }: FlipCardProps) {
  const [displayValue, setDisplayValue] = useState(value);
  const [previousValue, setPreviousValue] = useState(value);
  const [isFlipping, setIsFlipping] = useState(false);

  useEffect(() => {
    if (value !== displayValue) {
      setPreviousValue(displayValue);
      setIsFlipping(true);

      // Update the display value after half the animation
      const timer = setTimeout(() => {
        setDisplayValue(value);
      }, 300);

      // End the animation
      const endTimer = setTimeout(() => {
        setIsFlipping(false);
      }, 600);

      return () => {
        clearTimeout(timer);
        clearTimeout(endTimer);
      };
    }
  }, [value, displayValue]);

  return (
    <div className="relative w-16 h-24 md:w-24 md:h-36" style={{ perspective: '1000px' }}>
      {/* Top half background - always shows current value */}
      <div className="absolute top-0 left-0 w-full h-1/2 bg-primary text-primary-foreground rounded-t-lg overflow-hidden border-b border-primary-foreground/20">
        <div className="absolute inset-0 flex items-center justify-center">
          <div
            className="text-4xl md:text-6xl font-bold leading-none"
            style={{
              transform: 'translateY(50%)',
              clipPath: 'inset(0 0 50% 0)'
            }}
          >
            {displayValue}
          </div>
        </div>
      </div>

      {/* Bottom half background - always shows current value */}
      <div className="absolute bottom-0 left-0 w-full h-1/2 bg-primary text-primary-foreground rounded-b-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div
            className="text-4xl md:text-6xl font-bold leading-none"
            style={{
              transform: 'translateY(-50%)',
              clipPath: 'inset(50% 0 0 0)'
            }}
          >
            {displayValue}
          </div>
        </div>
      </div>

      {/* Animated top half - shows previous value and flips down */}
      {isFlipping && (
        <div
          className="absolute top-0 left-0 w-full h-1/2 bg-primary text-primary-foreground rounded-t-lg overflow-hidden border-b border-primary-foreground/20"
          style={{
            transformOrigin: 'bottom',
            transformStyle: 'preserve-3d',
            animation: 'flip-down 0.6s ease-in-out',
            zIndex: 10
          }}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <div
              className="text-4xl md:text-6xl font-bold leading-none"
              style={{
                transform: 'translateY(50%)',
                clipPath: 'inset(0 0 50% 0)'
              }}
            >
              {previousValue}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface FlipClockProps {
  time: number;
  className?: string;
}

export function FlipClock({ time, className }: FlipClockProps) {
  const minutes = String(Math.floor(time / 60)).padStart(2, '0');
  const seconds = String(time % 60).padStart(2, '0');

  const minuteTens = minutes[0];
  const minuteUnits = minutes[1];
  const secondTens = seconds[0];
  const secondUnits = seconds[1];

  return (
    <div className={cn("flex items-center gap-1 md:gap-2", className)}>
      <FlipCard value={minuteTens} />
      <FlipCard value={minuteUnits} />
      <div className="text-4xl md:text-6xl font-bold text-foreground animate-pulse">:</div>
      <FlipCard value={secondTens} />
      <FlipCard value={secondUnits} />
    </div>
  );
}
