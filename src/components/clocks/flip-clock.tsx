import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface FlipCardProps {
  value: string;
}

function FlipCard({ value }: FlipCardProps) {
  const [currentValue, setCurrentValue] = useState(value);
  const [oldValue, setOldValue] = useState(value);
  const [isFlipping, setIsFlipping] = useState(false);

  useEffect(() => {
    if (value !== currentValue) {
      setOldValue(currentValue);
      setCurrentValue(value);
      setIsFlipping(true);
      const timer = setTimeout(() => setIsFlipping(false), 600);
      return () => clearTimeout(timer);
    }
  }, [value, currentValue]);
  
  return (
    <div className="relative w-16 h-24 md:w-24 md:h-36 perspective-[1000px]">
      {/* Top Half (Static) */}
      <div className="absolute top-0 left-0 w-full h-1/2 bg-primary/80 text-primary-foreground rounded-t-lg overflow-hidden flex items-end justify-center">
        <span className="text-4xl md:text-6xl font-bold transform translate-y-1/2">{currentValue}</span>
      </div>
      {/* Bottom Half (Static) */}
      <div className="absolute bottom-0 left-0 w-full h-1/2 bg-primary/90 text-primary-foreground rounded-b-lg overflow-hidden flex items-start justify-center">
        <span className="text-4xl md:text-6xl font-bold transform -translate-y-1/2">{currentValue}</span>
      </div>

       {/* Top Half (Flipping) */}
      <div
        className={cn(
          "absolute top-0 left-0 w-full h-1/2 bg-primary text-primary-foreground rounded-t-lg overflow-hidden flex items-end justify-center origin-bottom transition-transform duration-500",
          {"transform-style-3d -rotate-x-180": isFlipping}
        )}
      >
        <span className="text-4xl md:text-6xl font-bold transform translate-y-1/2">{oldValue}</span>
      </div>

        {/* Bottom Half (Flipping) */}
      <div
        className={cn(
          "absolute bottom-0 left-0 w-full h-1/2 bg-primary text-primary-foreground rounded-b-lg overflow-hidden flex items-start justify-center origin-top transition-transform duration-500",
          {"transform-style-3d rotate-x-0": !isFlipping},
          {"transform-style-3d rotate-x-180": isFlipping}
        )}
      >
        <span className="text-4xl md:text-6xl font-bold transform -translate-y-1/2">{currentValue}</span>
      </div>
    </div>
  );
}

interface FlipClockProps {
  time: number;
  className?: string;
}

export function FlipClock({ time, className }: FlipClockProps) {
  const minutes = String(Math.floor(time / 60)).padStart(2, '0');
  const seconds = String(time % 60).padStart(2, '0');

  const minuteTens = minutes[0];
  const minuteUnits = minutes[1];
  const secondTens = seconds[0];
  const secondUnits = seconds[1];

  return (
    <div className={cn("flex items-center gap-1 md:gap-2", className)}>
      <FlipCard value={minuteTens} />
      <FlipCard value={minuteUnits} />
      <div className="text-4xl md:text-6xl font-bold text-foreground animate-pulse">:</div>
      <FlipCard value={secondTens} />
      <FlipCard value={secondUnits} />
    </div>
  );
}
