
import { cn } from '@/lib/utils';

interface AnalogClockProps {
  time: number;
  className?: string;
}

export function AnalogClock({ time, className }: AnalogClockProps) {
  const seconds = time % 60;
  const minutes = Math.floor(time / 60);
  
  const secondsDeg = (seconds / 60) * 360 + 90;
  const minutesDeg = (minutes / 60) * 360 + (seconds / 60) * 6 + 90;

  return (
    <div className={cn("relative w-48 h-48 md:w-64 md:h-64 rounded-full border-4 border-foreground bg-card", className)}>
      {/* Markings */}
      {[...Array(12)].map((_, i) => (
         <div key={i} className="absolute w-full h-full" style={{transform: `rotate(${i*30}deg)`}}>
            <div className={cn("absolute bg-muted-foreground w-1 h-3 top-0 left-1/2 -ml-0.5", {'h-5': i % 3 === 0})}></div>
         </div>
      ))}

      {/* Center dot */}
      <div className="absolute top-1/2 left-1/2 w-3 h-3 bg-primary rounded-full -translate-x-1/2 -translate-y-1/2 z-10"></div>

      {/* Hands */}
      <div className="absolute w-full h-full">
        {/* Minute Hand */}
        <div 
          className="absolute top-1/2 left-0 w-1/2 h-1 bg-foreground rounded-r-full origin-left"
          style={{ transform: `translateY(-50%) rotate(${minutesDeg}deg)` }}
        ></div>
        {/* Second Hand */}
        <div 
          className="absolute top-1/2 left-0 w-1/2 h-0.5 bg-accent rounded-r-full origin-left"
          style={{ transform: `translateY(-50%) rotate(${secondsDeg}deg)` }}
        ></div>
      </div>
    </div>
  );
}
