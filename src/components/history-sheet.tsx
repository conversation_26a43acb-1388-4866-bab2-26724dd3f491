'use client';
import { type ReactNode, useState, useEffect } from 'react';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
  SheetDescription,
} from '@/components/ui/sheet';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useLanguage } from '@/providers/language-provider';
import type { SessionRecord } from '@/types';
import { HISTORY_KEY } from '@/lib/constants';
import { format } from 'date-fns';

export function HistorySheet({ children }: { children: ReactNode }) {
  const { t } = useLanguage();
  const [history, setHistory] = useState<SessionRecord[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      try {
        const storedHistory = localStorage.getItem(HISTORY_KEY);
        if (storedHistory) {
          setHistory(JSON.parse(storedHistory));
        }
      } catch (e) {
        console.error("Failed to load history", e);
        setHistory([]);
      }
    }
  }, [isOpen]);
  
  const modeText = {
    focus: t('timer.focus'),
    shortBreak: t('timer.short_break'),
    longBreak: t('timer.long_break'),
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="w-full sm:w-[540px] flex flex-col">
        <SheetHeader>
          <SheetTitle>{t('history.title')}</SheetTitle>
          <SheetDescription>{t('app.name')}</SheetDescription>
        </SheetHeader>
        <div className="flex-grow overflow-y-auto mt-4">
            {history.length > 0 ? (
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>{t('history.table.type')}</TableHead>
                            <TableHead className="text-right">{t('history.table.duration')}</TableHead>
                            <TableHead className="text-right">{t('history.table.date')}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {history.map(session => (
                            <TableRow key={session.id}>
                                <TableCell className="font-medium">{modeText[session.type]}</TableCell>
                                <TableCell className="text-right">{session.duration}</TableCell>
                                <TableCell className="text-right">{format(new Date(session.completedAt), 'PPp')}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                    {t('history.empty')}
                </div>
            )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
