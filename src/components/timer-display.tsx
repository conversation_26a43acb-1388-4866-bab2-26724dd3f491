'use client';

import { useSettings } from '@/providers/settings-provider';
import { useTimer } from '@/providers/timer-provider';
import { DigitalClock } from './clocks/digital-clock';
import { AnalogClock } from './clocks/analog-clock';
import { VintageClock } from './clocks/vintage-clock';
import { useLanguage } from '@/providers/language-provider';
import { FlipClock } from './clocks/flip-clock';

export function TimerDisplay() {
  const { settings } = useSettings();
  const { timeLeft, mode } = useTimer();
  const { t } = useLanguage();

  const clockComponents = {
    digital: <DigitalClock time={timeLeft} />,
    analog: <AnalogClock time={timeLeft} />,
    vintage: <VintageClock time={timeLeft} />,
    flip: <FlipClock time={timeLeft} />,
  };

  const modeText = {
    focus: t('timer.focus'),
    shortBreak: t('timer.short_break'),
    longBreak: t('timer.long_break'),
  };

  return (
    <div className="flex flex-col items-center justify-center mb-8">
      <div className="bg-card/50 backdrop-blur-md rounded-lg shadow-lg p-2 md:p-4 mb-4">
        {clockComponents[settings.clockStyle]}
      </div>
      <p className="text-xl md:text-2xl font-medium text-foreground tracking-wider uppercase">
        {modeText[mode]}
      </p>
    </div>
  );
}
