
'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import Image from 'next/image';
import { useToast } from '@/hooks/use-toast';
import { Search } from 'lucide-react';

interface UnsplashSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImageSelect: (url: string) => void;
  initialQuery: string;
}

interface UnsplashImage {
  id: string;
  url: string;
  alt: string;
}

export function UnsplashSearchDialog({
  open,
  onOpenChange,
  onImageSelect,
  initialQuery,
}: UnsplashSearchDialogProps) {
  const [query, setQuery] = useState(initialQuery);
  const [images, setImages] = useState<UnsplashImage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSearch = async (e?: React.FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault();
    if (!query) return;

    setIsLoading(true);
    setImages([]);
    try {
      const response = await fetch(`/api/unsplash?query=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch images from Unsplash');
      }
      const data = await response.json();
      setImages(data.imageUrls);
       if (data.imageUrls.length === 0) {
        toast({
          title: 'No results',
          description: `No images found for "${query}". Try a different search term.`,
        });
      }
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Could not fetch images. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    if (open) {
        setQuery(initialQuery);
        handleSearch();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, initialQuery]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[80vw] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Search Unsplash</DialogTitle>
          <DialogDescription>
            Find the perfect background for your session.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSearch} className="flex w-full items-center space-x-2">
          <Input
            type="text"
            placeholder="e.g. 'peaceful forest', 'modern office'"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Searching...' : <Search className="h-4 w-4" />}
          </Button>
        </form>
        <ScrollArea className="flex-grow mt-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pr-4">
            {isLoading && [...Array(12)].map((_, i) => (
                <div key={i} className="aspect-video bg-muted animate-pulse rounded-md" />
            ))}
            {images.map((image) => (
              <div
                key={image.id}
                className="relative aspect-video cursor-pointer group rounded-md overflow-hidden"
                onClick={() => onImageSelect(image.url)}
              >
                <Image
                  src={image.url}
                  alt={image.alt || 'Unsplash image'}
                  fill
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                  className="object-cover transition-transform group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors" />
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
