
'use client';
import { type ReactNode, useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SheetDescription
} from '@/components/ui/sheet';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { useSettings } from '@/providers/settings-provider';
import { useLanguage } from '@/providers/language-provider';
import type { Settings, Theme, TimerMode, ColorPalette } from '@/types';
import { useToast } from '@/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, History } from 'lucide-react';
import { UnsplashSearchDialog } from './unsplash-search-dialog';
import { ScrollArea } from './ui/scroll-area';
import { useTheme } from '@/providers/theme-provider';
import { HistorySheet } from './history-sheet';
import { Separator } from './ui/separator';
import { cn } from '@/lib/utils';


const ColorPalettePreview = ({ colors }: { colors: string[] }) => (
    <div className="flex items-center gap-2">
      {colors.map((color, index) => (
        <div key={index} className="h-4 w-4 rounded-full" style={{ backgroundColor: color }} />
      ))}
    </div>
  );

export function SettingsSheet({ children }: { children: ReactNode }) {
  const { settings, setSettings, isLoaded } = useSettings();
  const { t } = useLanguage();
  const { toast } = useToast();
  const [localSettings, setLocalSettings] = useState<Settings>(settings);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [activeMode, setActiveMode] = useState<TimerMode>('focus');
  const { setTheme, setColorPalette } = useTheme();

  useEffect(() => {
    if (isLoaded) {
      setLocalSettings(settings);
    }
  }, [isLoaded, settings]);

  const handleSave = () => {
    setSettings(localSettings);
    toast({
      title: t('settings.saved'),
    });
  };

  const handleNotificationToggle = (enabled: boolean) => {
     if (enabled && Notification.permission !== 'granted') {
        Notification.requestPermission().then(permission => {
            if(permission === 'granted') {
                setLocalSettings(s => ({ ...s, notifications: { enabled: true } }));
            }
        });
     } else {
        setLocalSettings(s => ({ ...s, notifications: { ...s.notifications, enabled } }));
     }
  }
  
  const handleOpenSearch = (mode: TimerMode) => {
    setActiveMode(mode);
    setIsSearchOpen(true);
  };

  const handleImageSelect = (url: string) => {
    setLocalSettings(s => ({ ...s, backgrounds: { ...s.backgrounds, [activeMode]: { ...s.backgrounds[activeMode], url } } }));
    setIsSearchOpen(false);
  }

  const handleThemeChange = (value: string) => {
    const newTheme = value as Theme;
    setLocalSettings(s => ({...s, theme: newTheme}));
    setTheme(newTheme);
  };

  const handlePaletteChange = (value: string) => {
    const newPalette = value as ColorPalette;
    setLocalSettings(s => ({...s, colorPalette: newPalette}));
    setColorPalette(newPalette);
  }
  
  const handleSoundChange = (e: React.ChangeEvent<HTMLInputElement>, soundType: 'focusEnd' | 'shortBreakEnd' | 'longBreakEnd') => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const dataUrl = event.target?.result as string;
        setLocalSettings(s => ({
          ...s,
          sounds: {
            ...s.sounds,
            [soundType]: dataUrl,
          }
        }));
      };
      reader.readAsDataURL(file);
    }
  };


  if (!isLoaded) return children;

  const palettes = {
    default: {
        light: ['hsl(var(--background))','hsl(var(--primary))','hsl(var(--secondary))','hsl(var(--accent))'],
        dark: ['hsl(var(--background))','hsl(var(--primary))','hsl(var(--secondary))','hsl(var(--accent))'],
    },
    forest: {
        light: ['hsl(100 20% 96%)', 'hsl(95 35% 40%)', 'hsl(90 15% 90%)', 'hsl(40 60% 55%)'],
        dark: ['hsl(90 15% 8%)', 'hsl(95 25% 65%)', 'hsl(90 10% 12%)', 'hsl(40 50% 60%)'],
    },
    ocean: {
        light: ['hsl(210 40% 98%)', 'hsl(210 60% 50%)', 'hsl(210 30% 92%)', 'hsl(180 70% 45%)'],
        dark: ['hsl(220 25% 10%)', 'hsl(210 50% 70%)', 'hsl(220 15% 15%)', 'hsl(180 60% 55%)'],
    },
    sunset: {
        light: ['hsl(25 50% 98%)', 'hsl(15 80% 60%)', 'hsl(30 40% 94%)', 'hsl(340 70% 70%)'],
        dark: ['hsl(20 20% 12%)', 'hsl(20 70% 70%)', 'hsl(20 15% 20%)', 'hsl(330 65% 75%)'],
    },
    rose: {
        light: ['hsl(340 60% 98%)', 'hsl(340 60% 65%)', 'hsl(340 40% 94%)', 'hsl(280 50% 70%)'],
        dark: ['hsl(345 15% 10%)', 'hsl(340 55% 75%)', 'hsl(345 10% 15%)', 'hsl(280 45% 75%)'],
    }
  }
  
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    if (settings.theme === 'system') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        setEffectiveTheme(mediaQuery.matches ? 'dark' : 'light');
        const handler = (e: MediaQueryListEvent) => setEffectiveTheme(e.matches ? 'dark' : 'light');
        mediaQuery.addEventListener('change', handler);
        return () => mediaQuery.removeEventListener('change', handler);
    } else {
        setEffectiveTheme(settings.theme);
    }
  }, [settings.theme]);


  return (
    <>
      <Sheet>
        <SheetTrigger asChild>{children}</SheetTrigger>
        <SheetContent className="w-full sm:w-[540px] flex flex-col h-full max-h-screen">
          <SheetHeader>
            <SheetTitle>{t('settings.title')}</SheetTitle>
            <SheetDescription>{t('app.name')}</SheetDescription>
          </SheetHeader>

          <div className="flex-grow overflow-y-auto my-4 pr-6 landscape:pr-2">
            <div className="space-y-8 pb-4">
              {/* Timer Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{t('settings.timer')}</h3>
                <div className="space-y-2">
                  <Label htmlFor="focusDuration">{t('settings.timer.focus_duration')}</Label>
                  <Input id="focusDuration" type="number" value={localSettings.focusDuration} onChange={e => setLocalSettings(s => ({ ...s, focusDuration: Number(e.target.value) }))} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="shortBreakDuration">{t('settings.timer.short_break_duration')}</Label>
                  <Input id="shortBreakDuration" type="number" value={localSettings.shortBreakDuration} onChange={e => setLocalSettings(s => ({ ...s, shortBreakDuration: Number(e.target.value) }))} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="longBreakDuration">{t('settings.timer.long_break_duration')}</Label>
                  <Input id="longBreakDuration" type="number" value={localSettings.longBreakDuration} onChange={e => setLocalSettings(s => ({ ...s, longBreakDuration: Number(e.target.value) }))} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sessionsBeforeLongBreak">{t('settings.timer.sessions_before_long_break')}</Label>
                  <Input id="sessionsBeforeLongBreak" type="number" value={localSettings.sessionsBeforeLongBreak} onChange={e => setLocalSettings(s => ({ ...s, sessionsBeforeLongBreak: Number(e.target.value) }))} />
                </div>
              </div>

              <Separator />

              {/* Appearance Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{t('settings.appearance')}</h3>
                 <div className="space-y-2">
                    <Label>{t('settings.appearance.theme')}</Label>
                    <Select value={localSettings.theme} onValueChange={handleThemeChange}>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="light">{t('settings.appearance.theme.light')}</SelectItem>
                            <SelectItem value="dark">{t('settings.appearance.theme.dark')}</SelectItem>
                            <SelectItem value="system">{t('settings.appearance.theme.system')}</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.appearance.color_palette')}</Label>
                  <Select value={localSettings.colorPalette} onValueChange={handlePaletteChange}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                          <SelectItem value="default">
                              <div className="flex items-center gap-4">
                                  <ColorPalettePreview colors={palettes.default[effectiveTheme]} />
                                  <span>{t('settings.appearance.color_palette.default')}</span>
                              </div>
                          </SelectItem>
                          <SelectItem value="forest">
                               <div className="flex items-center gap-4">
                                  <ColorPalettePreview colors={palettes.forest[effectiveTheme]} />
                                  <span>{t('settings.appearance.color_palette.forest')}</span>
                              </div>
                          </SelectItem>
                          <SelectItem value="ocean">
                              <div className="flex items-center gap-4">
                                  <ColorPalettePreview colors={palettes.ocean[effectiveTheme]} />
                                  <span>{t('settings.appearance.color_palette.ocean')}</span>
                              </div>
                          </SelectItem>
                           <SelectItem value="sunset">
                              <div className="flex items-center gap-4">
                                  <ColorPalettePreview colors={palettes.sunset[effectiveTheme]} />
                                  <span>{t('settings.appearance.color_palette.sunset')}</span>
                              </div>
                          </SelectItem>
                           <SelectItem value="rose">
                              <div className="flex items-center gap-4">
                                  <ColorPalettePreview colors={palettes.rose[effectiveTheme]} />
                                  <span>{t('settings.appearance.color_palette.rose')}</span>
                              </div>
                          </SelectItem>
                      </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.appearance.clock_style')}</Label>
                  <Select value={localSettings.clockStyle} onValueChange={value => setLocalSettings(s => ({...s, clockStyle: value as any}))}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                          <SelectItem value="digital">{t('settings.appearance.clock_style.digital')}</SelectItem>
                          <SelectItem value="analog">{t('settings.appearance.clock_style.analog')}</SelectItem>
                          <SelectItem value="vintage">{t('settings.appearance.clock_style.vintage')}</SelectItem>
                          <SelectItem value="flip">{t('settings.appearance.clock_style.flip')}</SelectItem>
                      </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.appearance.backgrounds')}</Label>
                  <div className="flex items-center gap-2">
                      <Input placeholder={t('settings.appearance.backgrounds.focus_url')} value={localSettings.backgrounds.focus.url} onChange={e => setLocalSettings(s => ({...s, backgrounds: {...s.backgrounds, focus: {...s.backgrounds.focus, url: e.target.value }}}))} />
                      <Button variant="outline" size="icon" onClick={() => handleOpenSearch('focus')}>
                          <Search className="h-4 w-4" />
                      </Button>
                  </div>
                  <div className="flex items-center gap-2">
                      <Input placeholder={t('settings.appearance.backgrounds.short_break_url')} value={localSettings.backgrounds.shortBreak.url} onChange={e => setLocalSettings(s => ({...s, backgrounds: {...s.backgrounds, shortBreak: {...s.backgrounds.shortBreak, url: e.target.value }}}))} />
                       <Button variant="outline" size="icon" onClick={() => handleOpenSearch('shortBreak')}>
                          <Search className="h-4 w-4" />
                      </Button>
                  </div>
                  <div className="flex items-center gap-2">
                      <Input placeholder={t('settings.appearance.backgrounds.long_break_url')} value={localSettings.backgrounds.longBreak.url} onChange={e => setLocalSettings(s => ({...s, backgrounds: {...s.backgrounds, longBreak: {...s.backgrounds.longBreak, url: e.target.value }}}))} />
                       <Button variant="outline" size="icon" onClick={() => handleOpenSearch('longBreak')}>
                          <Search className="h-4 w-4" />
                      </Button>
                  </div>
                </div>
              </div>
              
              <Separator />

              {/* Sounds Section */}
              <div className="space-y-4">
                  <h3 className="text-lg font-medium">{t('settings.sounds')}</h3>
                  <div className="space-y-2">
                    <Label htmlFor="focusEndSound">{t('settings.sounds.focus_end')}</Label>
                    <Input id="focusEndSound" type="file" accept="audio/mpeg" onChange={e => handleSoundChange(e, 'focusEnd')} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shortBreakEndSound">{t('settings.sounds.short_break_end')}</Label>
                    <Input id="shortBreakEndSound" type="file" accept="audio/mpeg" onChange={e => handleSoundChange(e, 'shortBreakEnd')} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="longBreakEndSound">{t('settings.sounds.long_break_end')}</Label>
                    <Input id="longBreakEndSound" type="file" accept="audio/mpeg" onChange={e => handleSoundChange(e, 'longBreakEnd')} />
                  </div>
              </div>

              <Separator />

              {/* Notifications Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{t('settings.notifications')}</h3>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                      <Label htmlFor="notifications-switch">{t('settings.notifications.enable')}</Label>
                  </div>
                  <Switch id="notifications-switch" checked={localSettings.notifications.enabled} onCheckedChange={handleNotificationToggle} />
                </div>
              </div>
              
              <Separator />

              {/* History Section */}
               <div className="space-y-4">
                <h3 className="text-lg font-medium">{t('history.title')}</h3>
                 <HistorySheet>
                    <Button variant="outline" className="w-full">
                        <History className="mr-2 h-4 w-4" />
                        {t('settings.history.view_history')}
                    </Button>
                 </HistorySheet>
              </div>
            </div>
          </div>

          <SheetFooter className="mt-auto pt-4 border-t">
            <Button onClick={handleSave} className="w-full">{t('settings.save')}</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
      <UnsplashSearchDialog 
        open={isSearchOpen}
        onOpenChange={setIsSearchOpen}
        onImageSelect={handleImageSelect}
        initialQuery={localSettings.backgrounds[activeMode].hint}
      />
    </>
  );
}
